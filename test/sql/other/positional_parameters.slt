# Test basic positional parameter usage
query T
SELECT $1 as value FROM VALUES (1), (2), (3)
----
1
2
3

# Test positional parameters with multiple columns
query TT
SELECT $1 as first_col, $2 as second_col FROM VALUES (1, 'one'), (2, 'two'), (3, 'three')
----
1	one
2	two
3	three

# Test positional parameters in aggregated functions - SUM
query T
SELECT SUM($1) as total FROM VALUES (10), (20), (30)
----
60

# Test positional parameters in aggregated functions - MAX
query T
SELECT MAX($1) as maximum FROM VALUES (10), (20), (30)
----
30

# Test positional parameters in aggregated functions - MIN
query T
SELECT MIN($1) as minimum FROM VALUES (10), (20), (30)
----
10

# Test positional parameters in aggregated functions - AVG
query T
SELECT AVG($1) as average FROM VALUES (10), (20), (30)
----
20.000000

# Test positional parameters in aggregated functions - COUNT
query T
SELECT COUNT($1) as count_values FROM VALUES (10), (20), (30)
----
3

# Test multiple positional parameters in different aggregated functions
query TTT
SELECT SUM($1) as sum_first, MAX($2) as max_second, AVG($1) as avg_first
FROM VALUES (10, 100), (20, 200), (30, 300)
----
60	300	20.000000

# Test positional parameters with complex aggregations (similar to pivot functionality)
query TTTTTTTT
SELECT SUM($1) AS q1_total,
       SUM($2) AS q2_total,
       SUM($3) AS q3_total,
       SUM($4) AS q4_total,
       MAX($5) AS q1_max,
       MAX($6) AS q2_max,
       MAX($7) AS q3_max,
       MAX($8) AS q4_max
FROM VALUES (100, 200, 300, 400, 10, 20, 30, 40),
            (150, 250, 350, 450, 15, 25, 35, 45)
----
250	450	650	850	15	25	35	45

# Test positional parameters with GROUP BY (using multiple rows)
exclude-from-coverage
statement ok
CREATE OR REPLACE TABLE test_data(
  id INT,
  value1 INT,
  value2 INT,
  category TEXT)
AS SELECT * FROM VALUES
  (1, 100, 10, 'A'),
  (2, 200, 20, 'A'),
  (3, 300, 30, 'B'),
  (4, 400, 40, 'B')

query TTT
SELECT category, SUM($2) as sum_val1, MAX($3) as max_val2
FROM (SELECT category, value1, value2 FROM test_data)
GROUP BY category
ORDER BY category
----
A	300	20
B	700	40

# Test positional parameters with HAVING clause
query TT
SELECT SUM($1) as total, $2 as category
FROM VALUES (100, 'A'), (200, 'A'), (50, 'B'), (75, 'B')
GROUP BY $2
HAVING SUM($1) > 150
ORDER BY category
----
300	A

# Test positional parameters with ORDER BY
query TT
SELECT $1 as value, $2 as category
FROM VALUES (300, 'C'), (100, 'A'), (200, 'B')
ORDER BY $1
----
100	A
200	B
300	C

# Test positional parameters with WHERE clause
query T
SELECT $1 as value
FROM VALUES (10), (20), (30), (40)
WHERE $1 > 20
----
30
40

# Test positional parameters with mathematical operations
query TTT
SELECT $1 + $2 as sum_cols, $1 * $2 as product_cols, $1 - $2 as diff_cols
FROM VALUES (10, 5), (20, 3), (15, 7)
----
15	50	5
23	60	17
22	105	8

# Test positional parameters with string operations
query TT
SELECT $1 || '_' || $2 as concatenated, UPPER($2) as upper_second
FROM VALUES ('hello', 'world'), ('foo', 'bar')
----
hello_world	WORLD
foo_bar	BAR

# Test positional parameters with NULL handling
query TTT
SELECT $1 as first, $2 as second, COALESCE($2, 'default') as with_default
FROM VALUES (1, 'value'), (2, NULL), (3, 'another')
----
1	value	value
2	NULL	default
3	another	another

# Test positional parameters with nested aggregations
query TT
SELECT SUM($1) as total_sum, COUNT(DISTINCT $2) as unique_categories
FROM VALUES (100, 'A'), (200, 'A'), (300, 'B'), (400, 'B'), (500, 'C')
----
1500	3

# Test positional parameters with CASE expressions
query TT
SELECT $1 as value,
       CASE WHEN $1 > 200 THEN 'high'
            WHEN $1 > 100 THEN 'medium'
            ELSE 'low' END as category
FROM VALUES (50), (150), (250)
----
50	low
150	medium
250	high

# Test positional parameters with window functions
query TTT
SELECT $1 as value,
       SUM($1) OVER () as total_sum,
       ROW_NUMBER() OVER (ORDER BY $1) as row_num
FROM VALUES (10), (30), (20)
----
10	60	1
20	60	2
30	60	3

# Test positional parameters with VALUES clause (multiple columns)
query TT
SELECT $1 as first_col, $2 as second_col FROM VALUES (1, 'one'), (2, 'two'), (3, 'three')
----
1	one
2	two
3	three

# Test positional parameters in JOIN operations
query TT
SELECT v1.$2, v2.$2
  FROM (VALUES (1, 'one'), (2, 'two')) AS v1
        INNER JOIN (VALUES (1, 'One'), (3, 'three')) AS v2
  WHERE v2.$1 = v1.$1
----
one	One

# Test positional parameters in LEFT JOIN
query TTT
SELECT v1.$1, v1.$2, v2.$2
  FROM (VALUES (1, 'one'), (2, 'two'), (3, 'three')) AS v1
        LEFT JOIN (VALUES (1, 'ONE'), (2, 'TWO')) AS v2
  ON v1.$1 = v2.$1
ORDER BY v1.$1
----
1	one	ONE
2	two	TWO
3	three	NULL

# Test positional parameters with UNION
query T
SELECT $1 FROM VALUES (1), (2)
UNION
SELECT $1 FROM VALUES (3), (4)
ORDER BY $1
----
1
2
3
4

# Test positional parameters with subqueries
query TT
SELECT $1, $2
FROM (
  SELECT $1, $2
  FROM VALUES (10, 'ten'), (20, 'twenty'), (30, 'thirty')
  WHERE $1 > 15
)
----
20	twenty
30	thirty

# Clean up test table
exclude-from-coverage
statement ok
DROP TABLE IF EXISTS test_data