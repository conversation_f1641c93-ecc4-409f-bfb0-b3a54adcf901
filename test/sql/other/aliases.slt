# Test basic column aliases
query TT
SELECT 'text' as alias, alias
----
text	text

# Test multiple column aliases
query TTT
SELECT 'hello' AS greeting, 'world' AS target, greeting || ' ' || target AS message
----
hello	world	hello world

# Test numeric expression aliases
query III
SELECT 10 AS num1, 20 AS num2, num1 + num2 AS sum_result
----
10	20	30

# Test function aliases
query TI
SELECT 'hello world' AS text_val, LENGTH(text_val) AS text_length
----
hello world	11

# Test case-insensitive aliases
query TT
SELECT 'test' AS MyAlias, myalias AS lower_ref
----
test	test

# Setup test tables for more complex alias scenarios
exclude-from-coverage
statement ok
CREATE OR REPLACE TABLE employees (
    id INTEGER,
    name VARCHAR,
    department VARCHAR,
    salary INTEGER
);

exclude-from-coverage
statement ok
INSERT INTO employees VALUES
    (1, 'Alice', 'Engineering', 75000),
    (2, 'Bob', 'Sales', 60000),
    (3, 'Charlie', 'Engineering', 80000),
    (4, '<PERSON>', 'Marketing', 65000);

exclude-from-coverage
statement ok
CREATE OR REPLACE TABLE departments (
    dept_name VARCHAR,
    budget INTEGER
);

exclude-from-coverage
statement ok
INSERT INTO departments VALUES
    ('Engineering', 500000),
    ('Sales', 300000),
    ('Marketing', 200000);